#!/usr/bin/env python3
"""
AWS Load Balancer Controller Setup for Production SSL Termination
Installs and configures AWS Load Balancer Controller for ALB integration
"""

import logging
import subprocess
import tempfile
import os
import json
import boto3
from typing import Dict, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AWSLoadBalancerControllerSetup:
    """Manages AWS Load Balancer Controller installation and configuration."""
    
    def __init__(self, cluster_name: str = "architrave-cluster", region: str = "eu-central-1"):
        """Initialize AWS Load Balancer Controller Setup."""
        self.cluster_name = cluster_name
        self.region = region
        self.account_id = self.get_account_id()
        self.oidc_issuer = None
        
    def get_account_id(self) -> str:
        """Get AWS account ID."""
        try:
            sts_client = boto3.client('sts')
            response = sts_client.get_caller_identity()
            return response['Account']
        except Exception as e:
            logger.error(f"❌ Failed to get AWS account ID: {e}")
            return ""
    
    def get_oidc_issuer(self) -> Optional[str]:
        """Get OIDC issuer URL for the EKS cluster."""
        try:
            logger.info(f"🔍 Getting OIDC issuer for cluster: {self.cluster_name}")
            
            eks_client = boto3.client('eks', region_name=self.region)
            response = eks_client.describe_cluster(name=self.cluster_name)
            
            oidc_issuer = response['cluster']['identity']['oidc']['issuer']
            self.oidc_issuer = oidc_issuer
            logger.info(f"✅ OIDC issuer: {oidc_issuer}")
            return oidc_issuer
            
        except Exception as e:
            logger.error(f"❌ Failed to get OIDC issuer: {e}")
            return None
    
    def create_iam_policy(self) -> bool:
        """Create IAM policy for AWS Load Balancer Controller."""
        try:
            logger.info("📋 Creating IAM policy for AWS Load Balancer Controller...")
            
            # Download the IAM policy document
            policy_url = "https://raw.githubusercontent.com/kubernetes-sigs/aws-load-balancer-controller/v2.7.2/docs/install/iam_policy.json"
            
            result = subprocess.run([
                'curl', '-o', '/tmp/iam_policy.json', policy_url
            ], capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"❌ Failed to download IAM policy: {result.stderr}")
                return False
            
            # Create the IAM policy
            iam_client = boto3.client('iam')
            
            with open('/tmp/iam_policy.json', 'r') as f:
                policy_document = f.read()
            
            try:
                response = iam_client.create_policy(
                    PolicyName='AWSLoadBalancerControllerIAMPolicy',
                    PolicyDocument=policy_document,
                    Description='IAM policy for AWS Load Balancer Controller'
                )
                logger.info(f"✅ IAM policy created: {response['Policy']['Arn']}")
                return True
                
            except iam_client.exceptions.EntityAlreadyExistsException:
                logger.info("✅ IAM policy already exists")
                return True
                
        except Exception as e:
            logger.error(f"❌ Failed to create IAM policy: {e}")
            return False
    
    def create_service_account(self) -> bool:
        """Create service account with IAM role for AWS Load Balancer Controller."""
        try:
            logger.info("🔐 Creating service account for AWS Load Balancer Controller...")
            
            if not self.oidc_issuer:
                self.get_oidc_issuer()
            
            if not self.oidc_issuer:
                logger.error("❌ OIDC issuer not available")
                return False
            
            # Create service account using eksctl
            result = subprocess.run([
                'eksctl', 'create', 'iamserviceaccount',
                '--cluster', self.cluster_name,
                '--namespace', 'kube-system',
                '--name', 'aws-load-balancer-controller',
                '--role-name', 'AmazonEKSLoadBalancerControllerRole',
                '--attach-policy-arn', f'arn:aws:iam::{self.account_id}:policy/AWSLoadBalancerControllerIAMPolicy',
                '--approve'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✅ Service account created successfully")
                return True
            else:
                logger.error(f"❌ Failed to create service account: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to create service account: {e}")
            return False
    
    def install_controller_with_helm(self) -> bool:
        """Install AWS Load Balancer Controller using Helm."""
        try:
            logger.info("📦 Installing AWS Load Balancer Controller with Helm...")
            
            # Add EKS Helm repository
            result = subprocess.run([
                'helm', 'repo', 'add', 'eks', 'https://aws.github.io/eks-charts'
            ], capture_output=True, text=True)
            
            if result.returncode != 0 and "already exists" not in result.stderr:
                logger.error(f"❌ Failed to add Helm repository: {result.stderr}")
                return False
            
            # Update Helm repositories
            subprocess.run(['helm', 'repo', 'update'], capture_output=True, text=True)
            
            # Install AWS Load Balancer Controller
            result = subprocess.run([
                'helm', 'install', 'aws-load-balancer-controller', 'eks/aws-load-balancer-controller',
                '-n', 'kube-system',
                '--set', f'clusterName={self.cluster_name}',
                '--set', 'serviceAccount.create=false',
                '--set', 'serviceAccount.name=aws-load-balancer-controller',
                '--set', f'region={self.region}',
                '--set', 'vpcId=vpc-0123456789abcdef0'  # This will be auto-detected
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✅ AWS Load Balancer Controller installed successfully")
                return True
            else:
                logger.error(f"❌ Failed to install controller: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to install controller: {e}")
            return False
    
    def verify_installation(self) -> bool:
        """Verify AWS Load Balancer Controller installation."""
        try:
            logger.info("🔍 Verifying AWS Load Balancer Controller installation...")
            
            # Check if pods are running
            result = subprocess.run([
                'kubectl', 'get', 'pods', '-n', 'kube-system',
                '-l', 'app.kubernetes.io/name=aws-load-balancer-controller',
                '--no-headers'
            ], capture_output=True, text=True)
            
            if result.returncode == 0 and result.stdout.strip():
                pods = result.stdout.strip().split('\n')
                running_pods = [pod for pod in pods if 'Running' in pod]
                
                logger.info(f"✅ AWS Load Balancer Controller verification:")
                logger.info(f"   - Total pods: {len(pods)}")
                logger.info(f"   - Running pods: {len(running_pods)}")
                
                return len(running_pods) > 0
            else:
                logger.warning("⚠️ No AWS Load Balancer Controller pods found")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to verify installation: {e}")
            return False
    
    def setup_complete_alb_integration(self) -> bool:
        """Complete ALB integration setup."""
        try:
            logger.info("🚀 Setting up complete ALB integration...")
            
            # Step 1: Create IAM policy
            if not self.create_iam_policy():
                return False
            
            # Step 2: Create service account
            if not self.create_service_account():
                return False
            
            # Step 3: Install controller
            if not self.install_controller_with_helm():
                return False
            
            # Step 4: Verify installation
            if not self.verify_installation():
                return False
            
            logger.info("✅ Complete ALB integration setup successful!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to setup ALB integration: {e}")
            return False

def main():
    """Main function for AWS Load Balancer Controller setup."""
    import argparse
    
    parser = argparse.ArgumentParser(description='AWS Load Balancer Controller Setup')
    parser.add_argument('--cluster-name', default='architrave-cluster', help='EKS cluster name')
    parser.add_argument('--region', default='eu-central-1', help='AWS region')
    parser.add_argument('--action', choices=['install', 'verify'], 
                       default='install', help='Action to perform')
    
    args = parser.parse_args()
    
    # Initialize setup manager
    alb_setup = AWSLoadBalancerControllerSetup(args.cluster_name, args.region)
    
    if args.action == 'install':
        success = alb_setup.setup_complete_alb_integration()
        if success:
            logger.info("🎉 AWS Load Balancer Controller setup completed successfully!")
        else:
            logger.error("❌ AWS Load Balancer Controller setup failed!")
    elif args.action == 'verify':
        success = alb_setup.verify_installation()
        if success:
            logger.info("✅ AWS Load Balancer Controller is running correctly!")
        else:
            logger.error("❌ AWS Load Balancer Controller verification failed!")

if __name__ == '__main__':
    main()
