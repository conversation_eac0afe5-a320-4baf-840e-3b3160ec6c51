#!/usr/bin/env python3
"""
Frontend SSL Certificate Permission Fix Script
This script fixes SSL certificate permission issues in existing tenant frontend pods.
"""

import subprocess
import sys
import json
import time
from typing import List, Dict, Any

def run_command(command: str, check: bool = True) -> subprocess.CompletedProcess:
    """Run a shell command and return the result."""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, check=check)
        return result
    except subprocess.CalledProcessError as e:
        if check:
            print(f"❌ Command failed: {command}")
            print(f"Error: {e.stderr}")
            raise
        return e

def get_tenant_namespaces() -> List[str]:
    """Get all tenant namespaces."""
    result = run_command("kubectl get namespaces -o json")
    namespaces_data = json.loads(result.stdout)
    
    tenant_namespaces = []
    for ns in namespaces_data['items']:
        name = ns['metadata']['name']
        if name.startswith('tenant-'):
            tenant_namespaces.append(name)
    
    return tenant_namespaces

def check_frontend_pods(namespace: str) -> List[Dict[str, Any]]:
    """Check frontend pods in a namespace."""
    tenant_id = namespace.replace('tenant-', '')
    result = run_command(f"kubectl get pods -n {namespace} -l app=tenant-{tenant_id}-frontend -o json", check=False)
    if result.returncode != 0:
        return []
    
    pods_data = json.loads(result.stdout)
    frontend_pods = []
    
    for pod in pods_data['items']:
        pod_name = pod['metadata']['name']
        status = pod['status']['phase']
        
        # Check if pod is in CrashLoopBackOff
        container_statuses = pod['status'].get('containerStatuses', [])
        crash_loop = False
        for container in container_statuses:
            if container.get('state', {}).get('waiting', {}).get('reason') == 'CrashLoopBackOff':
                crash_loop = True
                break
        
        frontend_pods.append({
            'name': pod_name,
            'namespace': namespace,
            'status': status,
            'crash_loop': crash_loop
        })
    
    return frontend_pods

def fix_frontend_ssl_permissions(namespace: str, tenant_id: str) -> bool:
    """Fix SSL certificate permissions for frontend deployment."""
    print(f"🔧 Fixing SSL permissions for tenant {tenant_id} in namespace {namespace}")
    
    # Create the patch for SSL certificate permissions
    patch = {
        "spec": {
            "template": {
                "spec": {
                    "securityContext": {
                        "runAsUser": 101,
                        "runAsGroup": 101,
                        "fsGroup": 101,
                        "runAsNonRoot": True
                    },
                    "volumes": [{
                        "name": "ssl-certs",
                        "secret": {
                            "secretName": "ssl-certs",
                            "defaultMode": 0o644
                        }
                    }],
                    "containers": [{
                        "name": "frontend",
                        "securityContext": {
                            "runAsUser": 101,
                            "runAsGroup": 101,
                            "runAsNonRoot": True,
                            "allowPrivilegeEscalation": False,
                            "capabilities": {"drop": ["ALL"]}
                        },
                        "volumeMounts": [{
                            "name": "ssl-certs",
                            "mountPath": "/etc/nginx/cert",
                            "readOnly": True
                        }]
                    }]
                }
            }
        }
    }
    
    # Convert patch to JSON
    patch_json = json.dumps(patch)
    
    # Apply the patch
    try:
        result = run_command(f"kubectl patch deployment tenant-{tenant_id}-frontend -n {namespace} --type=strategic -p '{patch_json}'")
        print(f"✅ Applied SSL permission fix for tenant-{tenant_id}-frontend")
        
        # Wait for rollout to complete
        print(f"⏳ Waiting for rollout to complete...")
        run_command(f"kubectl rollout status deployment/tenant-{tenant_id}-frontend -n {namespace} --timeout=300s")
        print(f"✅ Rollout completed for tenant-{tenant_id}-frontend")
        
        return True
    except Exception as e:
        print(f"❌ Failed to fix SSL permissions for {tenant_id}: {e}")
        return False

def main():
    """Main function to fix frontend SSL issues."""
    print("🔧 Frontend SSL Certificate Permission Fix Script")
    print("=" * 60)
    
    # Get all tenant namespaces
    print("📋 Finding tenant namespaces...")
    tenant_namespaces = get_tenant_namespaces()
    
    if not tenant_namespaces:
        print("ℹ️ No tenant namespaces found.")
        return
    
    print(f"📋 Found {len(tenant_namespaces)} tenant namespaces: {', '.join(tenant_namespaces)}")
    
    # Check each namespace for frontend issues
    issues_found = 0
    fixes_applied = 0
    
    for namespace in tenant_namespaces:
        tenant_id = namespace.replace('tenant-', '')
        print(f"\n🔍 Checking namespace: {namespace}")
        
        # Check frontend pods
        frontend_pods = check_frontend_pods(namespace)
        
        if not frontend_pods:
            print(f"ℹ️ No frontend pods found in {namespace}")
            continue
        
        # Check for CrashLoopBackOff pods
        crash_pods = [pod for pod in frontend_pods if pod['crash_loop']]
        
        if crash_pods:
            print(f"🚨 Found {len(crash_pods)} frontend pods in CrashLoopBackOff:")
            for pod in crash_pods:
                print(f"   - {pod['name']}")
            
            # Check logs for SSL certificate errors
            for pod in crash_pods:
                result = run_command(f"kubectl logs {pod['name']} -n {namespace} --tail=10", check=False)
                if "Permission denied" in result.stdout and "architrave.key" in result.stdout:
                    print(f"🔍 SSL certificate permission issue detected in {pod['name']}")
                    issues_found += 1
                    
                    # Apply fix
                    if fix_frontend_ssl_permissions(namespace, tenant_id):
                        fixes_applied += 1
                    break
        else:
            print(f"✅ All frontend pods in {namespace} are healthy")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print(f"🔍 Tenant namespaces checked: {len(tenant_namespaces)}")
    print(f"🚨 SSL permission issues found: {issues_found}")
    print(f"🔧 Fixes applied: {fixes_applied}")
    
    if fixes_applied > 0:
        print("✅ Frontend SSL certificate permission fixes completed!")
        print("ℹ️ Please wait a few minutes for pods to restart and check their status.")
    else:
        print("ℹ️ No SSL permission issues found or no fixes needed.")

if __name__ == "__main__":
    main()
