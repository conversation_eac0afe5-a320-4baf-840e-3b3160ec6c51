#!/usr/bin/env python3
"""
Test script to verify onboarding script fixes
"""

import subprocess
import sys
import time

def run_command(command: str, check: bool = True) -> subprocess.CompletedProcess:
    """Run a shell command and return the result."""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, check=check)
        return result
    except subprocess.CalledProcessError as e:
        if check:
            print(f"❌ Command failed: {command}")
            print(f"Error: {e.stderr}")
            raise
        return e

def test_security_context_fixes():
    """Test that security context fixes don't have volume mount conflicts."""
    print("🔧 Testing security context fixes...")
    
    with open("advanced_tenant_onboard.py", "r") as f:
        content = f.read()
    
    # Check that we don't have conflicting volume mounts in security patches
    lines = content.split('\n')
    in_security_patch = False
    volume_mount_issues = []
    
    for i, line in enumerate(lines):
        if "frontend_security_patch" in line:
            in_security_patch = True
        elif in_security_patch and "}" in line and "spec" in line:
            in_security_patch = False
        elif in_security_patch and "volumeMounts" in line:
            # Check if this volume mount references a volume that doesn't exist in the patch
            if "ssl-certs" in line:
                # Look for corresponding volume definition
                volume_found = False
                for j in range(max(0, i-50), min(len(lines), i+50)):
                    if "volumes" in lines[j] and "ssl-certs" in lines[j]:
                        volume_found = True
                        break
                if not volume_found:
                    volume_mount_issues.append(f"Line {i+1}: Volume mount for ssl-certs without volume definition")
    
    if volume_mount_issues:
        print(f"❌ Found volume mount issues:")
        for issue in volume_mount_issues:
            print(f"   - {issue}")
        return False
    else:
        print("✅ Security context fixes are properly structured")
        return True

def test_comprehensive_security_variable():
    """Test that COMPREHENSIVE_SECURITY_ENABLED is properly initialized."""
    print("🔧 Testing COMPREHENSIVE_SECURITY_ENABLED variable...")
    
    with open("advanced_tenant_onboard.py", "r") as f:
        content = f.read()
    
    # Check for proper global declaration and initialization
    if "global COMPREHENSIVE_SECURITY_ENABLED" in content:
        print("✅ Found global declaration for COMPREHENSIVE_SECURITY_ENABLED")
        
        # Check for initialization check
        if "not 'COMPREHENSIVE_SECURITY_ENABLED' in globals()" in content:
            print("✅ Found proper initialization check")
            return True
        else:
            print("❌ Missing initialization check")
            return False
    else:
        print("❌ Missing global declaration")
        return False

def test_pod_stabilization_returncode():
    """Test that pod stabilization handles result objects properly."""
    print("🔧 Testing pod stabilization returncode handling...")
    
    with open("advanced_tenant_onboard.py", "r") as f:
        content = f.read()
    
    # Check for proper returncode handling
    if "hasattr(result, 'returncode')" in content:
        print("✅ Found proper returncode attribute check")
        
        # Check for string result handling
        if "isinstance(result, str)" in content:
            print("✅ Found string result handling")
            return True
        else:
            print("❌ Missing string result handling")
            return False
    else:
        print("❌ Missing returncode attribute check")
        return False

def test_import_statements():
    """Test that all required imports are present."""
    print("🔧 Testing import statements...")
    
    with open("advanced_tenant_onboard.py", "r") as f:
        content = f.read()
    
    required_imports = [
        "import tempfile",
        "import os",
        "import json"
    ]
    
    missing_imports = []
    for imp in required_imports:
        if imp not in content:
            missing_imports.append(imp)
    
    if missing_imports:
        print(f"❌ Missing imports:")
        for imp in missing_imports:
            print(f"   - {imp}")
        return False
    else:
        print("✅ All required imports are present")
        return True

def test_syntax_validation():
    """Test that the Python script has valid syntax."""
    print("🔧 Testing Python syntax validation...")
    
    try:
        result = run_command("python3 -m py_compile advanced_tenant_onboard.py", check=False)
        if result.returncode == 0:
            print("✅ Python syntax is valid")
            return True
        else:
            print(f"❌ Python syntax errors found:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Failed to check syntax: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing Onboarding Script Fixes")
    print("=" * 50)
    
    tests = [
        test_import_statements,
        test_syntax_validation,
        test_security_context_fixes,
        test_comprehensive_security_variable,
        test_pod_stabilization_returncode
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The onboarding script fixes are working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
