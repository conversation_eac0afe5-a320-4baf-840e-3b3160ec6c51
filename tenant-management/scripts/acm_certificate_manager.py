#!/usr/bin/env python3
"""
ACM Certificate Manager for Tenant Management System
Integrates AWS Certificate Manager with Kubernetes for production SSL certificates
"""

import boto3
import base64
import json
import logging
import subprocess
import tempfile
import os
from typing import Dict, Optional, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ACMCertificateManager:
    """Manages AWS ACM certificates for tenant deployments."""
    
    def __init__(self, region='eu-central-1'):
        """Initialize ACM Certificate Manager."""
        self.region = region
        self.acm_client = boto3.client('acm', region_name=region)
        
        # Your existing ACM certificate ARN
        self.certificate_arn = "arn:aws:acm:eu-central-1:545009857703:certificate/b1cd7c63-d434-4e29-b766-c0fec0253a16"
        
    def get_certificate_details(self) -> Optional[Dict]:
        """Get certificate details from ACM."""
        try:
            logger.info(f"🔍 Retrieving certificate details for ARN: {self.certificate_arn}")
            
            response = self.acm_client.describe_certificate(CertificateArn=self.certificate_arn)
            certificate = response['Certificate']
            
            logger.info(f"✅ Certificate found:")
            logger.info(f"   - Domain: {certificate['DomainName']}")
            logger.info(f"   - Status: {certificate['Status']}")
            logger.info(f"   - Subject Alternative Names: {certificate.get('SubjectAlternativeNames', [])}")
            
            return certificate
            
        except Exception as e:
            logger.error(f"❌ Failed to retrieve certificate details: {e}")
            return None
    
    def export_certificate(self) -> Optional[Tuple[str, str, str]]:
        """Export certificate, private key, and certificate chain from ACM."""
        try:
            logger.info(f"📥 Exporting certificate from ACM...")
            
            # Note: ACM certificates cannot be exported with private keys for security reasons
            # This method will get the certificate and chain, but private key is managed by AWS
            response = self.acm_client.get_certificate(CertificateArn=self.certificate_arn)
            
            certificate = response['Certificate']
            certificate_chain = response.get('CertificateChain', '')
            
            logger.info("✅ Certificate exported successfully")
            logger.warning("⚠️ Private key is managed by AWS and cannot be exported")
            
            return certificate, None, certificate_chain
            
        except Exception as e:
            logger.error(f"❌ Failed to export certificate: {e}")
            return None, None, None
    
    def create_kubernetes_secret(self, tenant_id: str, namespace: str = None) -> bool:
        """Create Kubernetes secret with ACM certificate for ALB integration."""
        if namespace is None:
            namespace = f"tenant-{tenant_id}"
            
        try:
            logger.info(f"🔐 Creating Kubernetes secret for tenant-{tenant_id}")
            
            # For ACM certificates, we create a secret with the certificate ARN
            # This will be used by AWS Load Balancer Controller
            secret_yaml = f"""
apiVersion: v1
kind: Secret
metadata:
  name: acm-certificate-arn
  namespace: {namespace}
  labels:
    app: ssl-certificate
    tenant: {tenant_id}
    certificate-type: acm
type: Opaque
data:
  certificate-arn: {base64.b64encode(self.certificate_arn.encode()).decode()}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ssl-certificate-config
  namespace: {namespace}
  labels:
    app: ssl-certificate
    tenant: {tenant_id}
data:
  certificate-arn: "{self.certificate_arn}"
  certificate-type: "acm"
  ssl-policy: "ELBSecurityPolicy-TLS-1-2-2017-01"
"""
            
            with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
                f.write(secret_yaml)
                temp_file = f.name
            
            try:
                result = subprocess.run(
                    ['kubectl', 'apply', '-f', temp_file],
                    capture_output=True,
                    text=True,
                    check=True
                )
                logger.info(f"✅ ACM certificate secret created for tenant-{tenant_id}")
                return True
                
            finally:
                os.unlink(temp_file)
                
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to create Kubernetes secret: {e.stderr}")
            return False
        except Exception as e:
            logger.error(f"❌ Failed to create Kubernetes secret: {e}")
            return False
    
    def create_alb_ingress(self, tenant_id: str, subdomain: str, domain: str = "architrave.com") -> bool:
        """Create ALB Ingress with ACM certificate integration."""
        namespace = f"tenant-{tenant_id}"
        
        try:
            logger.info(f"🌐 Creating ALB Ingress for tenant-{tenant_id}")
            
            ingress_yaml = f"""
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: tenant-{tenant_id}-alb-ingress
  namespace: {namespace}
  labels:
    app: tenant-{tenant_id}-ingress
    tenant: {tenant_id}
  annotations:
    # AWS Load Balancer Controller annotations
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{{"HTTP": 80}}, {{"HTTPS": 443}}]'
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/certificate-arn: {self.certificate_arn}
    alb.ingress.kubernetes.io/ssl-policy: ELBSecurityPolicy-TLS-1-2-2017-01
    alb.ingress.kubernetes.io/healthcheck-path: /health
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '30'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '5'
    alb.ingress.kubernetes.io/healthy-threshold-count: '2'
    alb.ingress.kubernetes.io/unhealthy-threshold-count: '3'
spec:
  rules:
  - host: {subdomain}.{domain}
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: tenant-{tenant_id}-frontend
            port:
              number: 80
  - host: "*.{subdomain}.{domain}"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: tenant-{tenant_id}-frontend
            port:
              number: 80
"""
            
            with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
                f.write(ingress_yaml)
                temp_file = f.name
            
            try:
                result = subprocess.run(
                    ['kubectl', 'apply', '-f', temp_file],
                    capture_output=True,
                    text=True,
                    check=True
                )
                logger.info(f"✅ ALB Ingress created for tenant-{tenant_id}")
                return True
                
            finally:
                os.unlink(temp_file)
                
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to create ALB Ingress: {e.stderr}")
            return False
        except Exception as e:
            logger.error(f"❌ Failed to create ALB Ingress: {e}")
            return False
    
    def verify_certificate_integration(self, tenant_id: str) -> bool:
        """Verify ACM certificate integration for tenant."""
        namespace = f"tenant-{tenant_id}"
        
        try:
            logger.info(f"🔍 Verifying ACM certificate integration for tenant-{tenant_id}")
            
            # Check if secret exists
            result = subprocess.run(
                ['kubectl', 'get', 'secret', 'acm-certificate-arn', '-n', namespace],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                logger.info(f"✅ ACM certificate secret found for tenant-{tenant_id}")
                
                # Check if ingress exists
                result = subprocess.run(
                    ['kubectl', 'get', 'ingress', f'tenant-{tenant_id}-alb-ingress', '-n', namespace],
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    logger.info(f"✅ ALB Ingress found for tenant-{tenant_id}")
                    return True
                else:
                    logger.warning(f"⚠️ ALB Ingress not found for tenant-{tenant_id}")
                    return False
            else:
                logger.warning(f"⚠️ ACM certificate secret not found for tenant-{tenant_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to verify certificate integration: {e}")
            return False

def main():
    """Main function for testing ACM Certificate Manager."""
    import argparse
    
    parser = argparse.ArgumentParser(description='ACM Certificate Manager')
    parser.add_argument('--tenant-id', required=True, help='Tenant ID')
    parser.add_argument('--subdomain', required=True, help='Subdomain for tenant')
    parser.add_argument('--domain', default='architrave.com', help='Domain name')
    parser.add_argument('--action', choices=['setup', 'verify', 'details'], 
                       default='setup', help='Action to perform')
    
    args = parser.parse_args()
    
    # Initialize ACM Certificate Manager
    acm_manager = ACMCertificateManager()
    
    if args.action == 'details':
        acm_manager.get_certificate_details()
    elif args.action == 'setup':
        # Setup ACM certificate integration
        acm_manager.create_kubernetes_secret(args.tenant_id)
        acm_manager.create_alb_ingress(args.tenant_id, args.subdomain, args.domain)
    elif args.action == 'verify':
        acm_manager.verify_certificate_integration(args.tenant_id)

if __name__ == '__main__':
    main()
