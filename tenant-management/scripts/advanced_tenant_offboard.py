#!/usr/bin/env python3
"""
Advanced Tenant Offboarding Script
----------------------------------
This script provides an enhanced tenant offboarding experience with:
- Rich terminal output with colors and formatting
- Table-based status displays
- Progress indicators for long-running operations
- Parallel execution of independent tasks
- Comprehensive verification and validation
"""

import argparse
import asyncio
import boto3
import json
import logging
import os
import re
import shutil
import subprocess
import sys
import tempfile
import time
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Union

# Import security modules
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from security.credentials import get_rds_credentials
    from security.validators import validate_tenant_id, ValidationError
except ImportError:
    print("Warning: Security modules not found. Some security features may not work.")
    get_rds_credentials = None
    validate_tenant_id = None
    ValidationError = Exception

# Import Hetzner DNS manager
try:
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'hetzner-dns'))
    from hetzner_dns_manager import HetznerDNSManager
except ImportError:
    print("Warning: Hetzner DNS manager not found. DNS cleanup will be disabled.")
    HetznerDNSManager = None

# Third-party libraries for enhanced terminal output
try:
    from rich.console import Console
    from rich.table import Table
    from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
    from rich.panel import Panel
    from rich.logging import RichHandler
    RICH_AVAILABLE = True
except ImportError:
    print("Rich library not found. Installing...")
    subprocess.run([sys.executable, "-m", "pip", "install", "rich"], check=True)
    from rich.console import Console
    from rich.table import Table
    from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
    from rich.panel import Panel
    from rich.logging import RichHandler
    RICH_AVAILABLE = True

# Initialize Rich console
console = Console()

# Configure logging with Rich
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(rich_tracebacks=True, markup=True)]
)
logger = logging.getLogger("tenant-offboarding")

# AWS clients
s3_client = boto3.client('s3')
rds_client = boto3.client('rds')

# Constants
DEFAULT_RDS_SECRET_NAME = "production/rds/credentials"
DEFAULT_BACKUP_PATH = "./backups"

# Helper functions for enhanced terminal output
def print_header(title: str) -> None:
    """Print a formatted header."""
    console.print(Panel(f"[bold blue]{title}[/bold blue]", expand=False))

def print_step(step: str, description: str) -> None:
    """Print a formatted step."""
    console.print(f"[bold cyan]STEP {step}:[/bold cyan] {description}")

def print_success(message: str) -> None:
    """Print a success message."""
    console.print(f"[bold green]✓ SUCCESS:[/bold green] {message}")

def print_warning(message: str) -> None:
    """Print a warning message."""
    console.print(f"[bold yellow]⚠ WARNING:[/bold yellow] {message}")

def print_error(message: str) -> None:
    """Print an error message."""
    console.print(f"[bold red]✗ ERROR:[/bold red] {message}")

def print_info(message: str) -> None:
    """Print an info message."""
    console.print(f"[bold white]ℹ INFO:[/bold white] {message}")

def create_status_table(title: str, headers: List[str]) -> Table:
    """Create a Rich table for status display."""
    table = Table(title=title)
    for header in headers:
        table.add_column(header, style="cyan")
    return table

def run_command(command: str, check: bool = True) -> subprocess.CompletedProcess:
    """Run a shell command and return the result."""
    print_info(f"Running command: {command}")
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=check,
            text=True,
            capture_output=True
        )
        if result.returncode == 0:
            if result.stdout.strip():
                print_info(f"Command output: {result.stdout.strip()}")
        else:
            print_error(f"Command failed with exit code {result.returncode}")
            print_error(f"Error output: {result.stderr.strip()}")
        return result
    except subprocess.CalledProcessError as e:
        print_error(f"Command execution failed: {e}")
        raise

# Core functionality
def create_backup(tenant_id: str, backup_path: str) -> bool:
    """Create a backup of the tenant's data."""
    print_step("1", f"Creating backup for tenant-{tenant_id}")

    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    backup_dir = os.path.join(backup_path, f"tenant_{tenant_id}_backup_{timestamp}")

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TimeElapsedColumn(),
        console=console
    ) as progress:
        task = progress.add_task(f"Creating backup for tenant-{tenant_id}...", total=100)

        # Create backup directory
        progress.update(task, advance=10, description="Creating backup directory...")
        os.makedirs(backup_dir, exist_ok=True)

        # Export Kubernetes resources
        progress.update(task, advance=30, description="Exporting Kubernetes resources...")
        try:
            run_command(f"kubectl get all -n tenant-{tenant_id} -o yaml > {backup_dir}/kubernetes_resources.yaml", check=False)
            run_command(f"kubectl get configmap -n tenant-{tenant_id} -o yaml > {backup_dir}/configmaps.yaml", check=False)
            run_command(f"kubectl get secret -n tenant-{tenant_id} -o yaml > {backup_dir}/secrets.yaml", check=False)
            run_command(f"kubectl get virtualservice,destinationrule,peerauthentication -n tenant-{tenant_id} -o yaml > {backup_dir}/istio_resources.yaml", check=False)
        except Exception as e:
            print_warning(f"Failed to export some Kubernetes resources: {e}")

        # Export database
        progress.update(task, advance=30, description="Exporting database...")
        try:
            # Get database credentials
            db_name = f"tenant_{tenant_id.replace('-', '_')}"

            # Get RDS credentials from AWS Secrets Manager
            try:
                if get_rds_credentials:
                    rds_credentials = get_rds_credentials("production/rds/credentials")
                    rds_host = rds_credentials['host']
                    rds_port = rds_credentials['port']
                    rds_user = rds_credentials['username']
                    rds_password = rds_credentials['password']
                else:
                    print_error("Security modules not available. Cannot retrieve RDS credentials.")
                    return False
            except Exception as e:
                print_error(f"Failed to retrieve RDS credentials: {e}")
                return False

            # Try to get RDS credentials from secret
            try:
                result = run_command(f"kubectl get secret {DEFAULT_RDS_SECRET_NAME} -o jsonpath='{{.data.username}}' | base64 --decode", check=False)
                if result.returncode == 0 and result.stdout.strip():
                    rds_user = result.stdout.strip()

                result = run_command(f"kubectl get secret {DEFAULT_RDS_SECRET_NAME} -o jsonpath='{{.data.password}}' | base64 --decode", check=False)
                if result.returncode == 0 and result.stdout.strip():
                    rds_password = result.stdout.strip()

                result = run_command(f"kubectl get secret {DEFAULT_RDS_SECRET_NAME} -o jsonpath='{{.data.host}}' | base64 --decode", check=False)
                if result.returncode == 0 and result.stdout.strip():
                    rds_host = result.stdout.strip()

                result = run_command(f"kubectl get secret {DEFAULT_RDS_SECRET_NAME} -o jsonpath='{{.data.port}}' | base64 --decode", check=False)
                if result.returncode == 0 and result.stdout.strip():
                    rds_port = result.stdout.strip()
            except Exception as e:
                print_warning(f"Failed to get RDS credentials from secret: {e}")
                print_info("Using default RDS credentials")

            # Create database bastion pod
            bastion_yaml = f"""
apiVersion: v1
kind: Pod
metadata:
  name: db-backup-{tenant_id}
  annotations:
    sidecar.istio.io/inject: "false"
spec:
  securityContext:
    runAsNonRoot: true
    runAsUser: 999
    runAsGroup: 999
    fsGroup: 999
    seccompProfile:
      type: RuntimeDefault
  containers:
  - name: mysql-client
    image: mysql:8.0
    command: ["sleep", "infinity"]
    securityContext:
      allowPrivilegeEscalation: false
      runAsNonRoot: true
      runAsUser: 999
      runAsGroup: 999
      readOnlyRootFilesystem: false
      capabilities:
        drop:
        - ALL
      seccompProfile:
        type: RuntimeDefault
    resources:
      limits:
        cpu: 200m
        memory: 256Mi
      requests:
        cpu: 100m
        memory: 128Mi
    volumeMounts:
    - name: tmp-volume
      mountPath: /tmp
  volumes:
  - name: tmp-volume
    emptyDir: {{}}
  restartPolicy: Never
"""
            with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
                f.write(bastion_yaml)
                bastion_file = f.name

            run_command(f"kubectl apply -f {bastion_file}")
            os.unlink(bastion_file)

            # Wait for bastion pod to be ready
            run_command(f"kubectl wait --for=condition=ready pod/db-backup-{tenant_id} --timeout=60s")

            # Export database
            run_command(f"""
            kubectl exec db-backup-{tenant_id} -- bash -c "MYSQL_PWD='{rds_password}' mysqldump -h {rds_host} -P {rds_port} -u {rds_user} --databases {db_name} > /tmp/database_backup.sql"
            """)

            # Copy database backup to local
            run_command(f"kubectl cp db-backup-{tenant_id}:/tmp/database_backup.sql {backup_dir}/database_backup.sql")

            # Clean up bastion pod
            run_command(f"kubectl delete pod db-backup-{tenant_id}")
        except Exception as e:
            print_warning(f"Failed to export database: {e}")

        # Export S3 bucket
        progress.update(task, advance=30, description="Exporting S3 bucket...")
        try:
            bucket_name = f"tenant-{tenant_id}-assets"
            run_command(f"aws s3 sync s3://{bucket_name} {backup_dir}/s3_bucket", check=False)
        except Exception as e:
            print_warning(f"Failed to export S3 bucket: {e}")

        progress.update(task, completed=100)

    print_success(f"Backup for tenant-{tenant_id} created successfully at {backup_dir}")
    return True

def cleanup_monitoring(tenant_id: str) -> bool:
    """Clean up monitoring resources for the tenant."""
    print_step("2", f"Cleaning up monitoring resources for tenant-{tenant_id}")

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TimeElapsedColumn(),
        console=console
    ) as progress:
        task = progress.add_task(f"Cleaning up monitoring resources for tenant-{tenant_id}...", total=100)

        # Delete ServiceMonitor
        progress.update(task, advance=40, description="Deleting ServiceMonitor...")
        run_command(f"kubectl delete servicemonitor tenant-{tenant_id}-monitor -n tenant-{tenant_id} --ignore-not-found", check=False)

        # Delete PrometheusRule
        progress.update(task, advance=40, description="Deleting PrometheusRule...")
        run_command(f"kubectl delete prometheusrule tenant-{tenant_id}-alerts -n tenant-{tenant_id} --ignore-not-found", check=False)

        # Delete Grafana dashboard
        progress.update(task, advance=20, description="Deleting Grafana dashboard...")
        run_command(f"kubectl delete configmap tenant-{tenant_id}-dashboard -n monitoring --ignore-not-found", check=False)

        progress.update(task, completed=100)

    print_success(f"Monitoring resources for tenant-{tenant_id} cleaned up successfully")
    return True

def cleanup_autoscaling(tenant_id: str) -> bool:
    """Clean up autoscaling resources for the tenant."""
    print_step("3", f"Cleaning up autoscaling resources for tenant-{tenant_id}")

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TimeElapsedColumn(),
        console=console
    ) as progress:
        task = progress.add_task(f"Cleaning up autoscaling resources for tenant-{tenant_id}...", total=100)

        # Delete HorizontalPodAutoscalers
        progress.update(task, advance=50, description="Deleting HorizontalPodAutoscalers...")
        run_command(f"kubectl delete hpa -n tenant-{tenant_id} --all --ignore-not-found", check=False)

        # Delete custom metrics
        progress.update(task, advance=50, description="Deleting custom metrics...")
        # TODO: Add custom metrics cleanup

        progress.update(task, completed=100)

    print_success(f"Autoscaling resources for tenant-{tenant_id} cleaned up successfully")
    return True

def cleanup_hetzner_dns(tenant_id: str, hetzner_dns_token: str = None, dns_zone: str = "architrave.com") -> bool:
    """Clean up Hetzner DNS records for the tenant."""
    if not hetzner_dns_token or not HetznerDNSManager:
        print_warning("⚠️ Hetzner DNS token not provided or DNS manager not available. Skipping DNS cleanup.")
        return True

    print_step("3.1", f"Cleaning up Hetzner DNS for tenant-{tenant_id}")

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TimeElapsedColumn(),
        console=console
    ) as progress:
        task = progress.add_task(f"Cleaning up DNS records for tenant-{tenant_id}...", total=100)

        try:
            # Initialize DNS manager
            progress.update(task, advance=20, description="Initializing DNS manager...")
            dns_manager = HetznerDNSManager(hetzner_dns_token, dns_zone)

            # Get DNS record IDs from namespace annotation
            progress.update(task, advance=20, description="Getting DNS record IDs...")
            record_ids = get_dns_record_ids_from_namespace(tenant_id)

            # Clean up DNS records
            progress.update(task, advance=40, description="Deleting DNS records...")
            success = dns_manager.cleanup_tenant_dns(tenant_id, record_ids)

            progress.update(task, advance=20, description="Verifying DNS cleanup...")
            if success:
                print_success(f"Hetzner DNS cleanup completed for tenant-{tenant_id}")
                progress.update(task, completed=100)
                return True
            else:
                print_warning(f"Hetzner DNS cleanup completed with some issues for tenant-{tenant_id}")
                progress.update(task, completed=100)
                return False

        except Exception as e:
            print_error(f"❌ DNS cleanup failed for tenant-{tenant_id}: {e}")
            progress.update(task, completed=100)
            return False

def get_dns_record_ids_from_namespace(tenant_id: str) -> dict:
    """Get DNS record IDs from namespace annotation."""
    try:
        import json
        namespace_name = f"tenant-{tenant_id}"

        # Get namespace annotation
        result = run_command(
            f"kubectl get namespace {namespace_name} -o jsonpath='{{.metadata.annotations.hetzner-dns/record-ids}}'",
            check=False
        )

        if result.returncode == 0 and result.stdout.strip():
            record_ids = json.loads(result.stdout.strip())
            print_info(f"Found DNS record IDs in namespace annotation: {record_ids}")
            return record_ids
        else:
            print_info("No DNS record IDs found in namespace annotation")
            return {}

    except Exception as e:
        print_warning(f"Failed to get DNS record IDs from namespace: {e}")
        return {}

def cleanup_namespace(tenant_id: str, force: bool = False) -> bool:
    """Clean up the Kubernetes namespace for the tenant."""
    print_step("4", f"Cleaning up namespace for tenant-{tenant_id}")

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TimeElapsedColumn(),
        console=console
    ) as progress:
        task = progress.add_task(f"Cleaning up namespace for tenant-{tenant_id}...", total=100)

        # Check if namespace exists
        progress.update(task, advance=10, description="Checking if namespace exists...")
        result = run_command(f"kubectl get namespace tenant-{tenant_id} 2>/dev/null || echo 'not found'", check=False)
        if "not found" in result.stdout:
            print_warning(f"Namespace tenant-{tenant_id} does not exist")
            progress.update(task, completed=100)
            return True

        # Delete Istio resources
        progress.update(task, advance=20, description="Deleting Istio resources...")
        run_command(f"kubectl delete virtualservice,destinationrule,peerauthentication -n tenant-{tenant_id} --all --ignore-not-found", check=False)

        # Delete deployments
        progress.update(task, advance=20, description="Deleting deployments...")
        run_command(f"kubectl delete deployment -n tenant-{tenant_id} --all --ignore-not-found", check=False)

        # Delete services
        progress.update(task, advance=10, description="Deleting services...")
        run_command(f"kubectl delete service -n tenant-{tenant_id} --all --ignore-not-found", check=False)

        # Delete ConfigMaps and secrets
        progress.update(task, advance=10, description="Deleting ConfigMaps and secrets...")
        run_command(f"kubectl delete configmap,secret -n tenant-{tenant_id} --all --ignore-not-found", check=False)

        # Delete pods
        progress.update(task, advance=10, description="Deleting pods...")
        run_command(f"kubectl delete pod -n tenant-{tenant_id} --all --ignore-not-found", check=False)

        # Delete namespace
        progress.update(task, advance=20, description="Deleting namespace...")
        run_command(f"kubectl delete namespace tenant-{tenant_id} --ignore-not-found", check=False)

        progress.update(task, completed=100)

    # Use force cleanup to handle finalizers and stuck namespaces
    if not force_namespace_cleanup(tenant_id):
        print_error(f"Failed to delete namespace tenant-{tenant_id}")
        return False

    print_success(f"Namespace tenant-{tenant_id} cleaned up successfully")
    return True

def cleanup_database(tenant_id: str, rds_secret_name: str) -> bool:
    """Clean up the database for the tenant."""
    print_step("5", f"Cleaning up database for tenant-{tenant_id}")

    # Convert tenant_id with hyphens to underscores for database name
    db_name = f"tenant_{tenant_id.replace('-', '_')}"
    db_user = f"tenant_{tenant_id.replace('-', '_')}"

    # Get RDS credentials from AWS Secrets Manager
    try:
        import boto3
        import json

        secrets_client = boto3.client('secretsmanager', region_name='eu-central-1')
        response = secrets_client.get_secret_value(SecretId=rds_secret_name)
        rds_credentials = json.loads(response['SecretString'])

        rds_host = rds_credentials['host']
        rds_port = rds_credentials['port']
        rds_user = rds_credentials['username']
        rds_password = rds_credentials['password']

        logger.info("Successfully retrieved RDS credentials from Secrets Manager")
    except Exception as e:
        logger.error(f"Failed to retrieve RDS credentials from Secrets Manager: {e}")
        raise

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TimeElapsedColumn(),
        console=console
    ) as progress:
        task = progress.add_task(f"Cleaning up database {db_name}...", total=100)

        # Try to get RDS credentials from secret
        progress.update(task, advance=10, description="Getting RDS credentials...")
        try:
            result = run_command(f"kubectl get secret {rds_secret_name} -o jsonpath='{{.data.username}}' | base64 --decode", check=False)
            if result.returncode == 0 and result.stdout.strip():
                rds_user = result.stdout.strip()

            result = run_command(f"kubectl get secret {rds_secret_name} -o jsonpath='{{.data.password}}' | base64 --decode", check=False)
            if result.returncode == 0 and result.stdout.strip():
                rds_password = result.stdout.strip()

            result = run_command(f"kubectl get secret {rds_secret_name} -o jsonpath='{{.data.host}}' | base64 --decode", check=False)
            if result.returncode == 0 and result.stdout.strip():
                rds_host = result.stdout.strip()

            result = run_command(f"kubectl get secret {rds_secret_name} -o jsonpath='{{.data.port}}' | base64 --decode", check=False)
            if result.returncode == 0 and result.stdout.strip():
                rds_port = result.stdout.strip()
        except Exception as e:
            print_warning(f"Failed to get RDS credentials from secret: {e}")
            print_info("Using default RDS credentials")

        # Create database cleanup pod
        progress.update(task, advance=20, description="Creating database cleanup pod...")
        bastion_yaml = f"""
apiVersion: v1
kind: Pod
metadata:
  name: db-cleanup-{tenant_id}
  annotations:
    sidecar.istio.io/inject: "false"
spec:
  securityContext:
    runAsNonRoot: true
    runAsUser: 999
    runAsGroup: 999
    fsGroup: 999
    seccompProfile:
      type: RuntimeDefault
  containers:
  - name: mysql-client
    image: mysql:8.0
    command: ["sleep", "infinity"]
    securityContext:
      allowPrivilegeEscalation: false
      runAsNonRoot: true
      runAsUser: 999
      runAsGroup: 999
      readOnlyRootFilesystem: false
      capabilities:
        drop:
        - ALL
      seccompProfile:
        type: RuntimeDefault
    resources:
      limits:
        cpu: 200m
        memory: 256Mi
      requests:
        cpu: 100m
        memory: 128Mi
    volumeMounts:
    - name: tmp-volume
      mountPath: /tmp
  volumes:
  - name: tmp-volume
    emptyDir: {{}}
  restartPolicy: Never
"""
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write(bastion_yaml)
            bastion_file = f.name

        run_command(f"kubectl apply -f {bastion_file}")
        os.unlink(bastion_file)

        # Wait for bastion pod to be ready
        run_command(f"kubectl wait --for=condition=ready pod/db-cleanup-{tenant_id} --timeout=60s")

        # Check if database exists
        progress.update(task, advance=20, description="Checking if database exists...")
        result = run_command(f"""
        kubectl exec db-cleanup-{tenant_id} -- bash -c "MYSQL_PWD='{rds_password}' mysql -h {rds_host} -P {rds_port} -u {rds_user} -e \\"SHOW DATABASES LIKE '{db_name}'\\""
        """, check=False)

        if db_name in result.stdout:
            # Drop database
            progress.update(task, advance=20, description=f"Dropping database {db_name}...")
            run_command(f"""
            kubectl exec db-cleanup-{tenant_id} -- bash -c "MYSQL_PWD='{rds_password}' mysql -h {rds_host} -P {rds_port} -u {rds_user} -e \\"DROP DATABASE {db_name}\\""
            """, check=False)
        else:
            print_warning(f"Database {db_name} does not exist")
            progress.update(task, advance=20)

        # Check if user exists
        progress.update(task, advance=10, description="Checking if user exists...")
        result = run_command(f"""
        kubectl exec db-cleanup-{tenant_id} -- bash -c "MYSQL_PWD='{rds_password}' mysql -h {rds_host} -P {rds_port} -u {rds_user} -e \\"SELECT User FROM mysql.user WHERE User='{db_user}'\\""
        """, check=False)

        if db_user in result.stdout:
            # Drop user
            progress.update(task, advance=10, description=f"Dropping user {db_user}...")
            run_command(f"""
            kubectl exec db-cleanup-{tenant_id} -- bash -c "MYSQL_PWD='{rds_password}' mysql -h {rds_host} -P {rds_port} -u {rds_user} -e \\"DROP USER '{db_user}'@'%'\\""
            """, check=False)

            # Flush privileges
            run_command(f"""
            kubectl exec db-cleanup-{tenant_id} -- bash -c "MYSQL_PWD='{rds_password}' mysql -h {rds_host} -P {rds_port} -u {rds_user} -e \\"FLUSH PRIVILEGES\\""
            """, check=False)
        else:
            print_warning(f"User {db_user} does not exist")
            progress.update(task, advance=10)

        # Clean up bastion pod
        progress.update(task, advance=10, description="Cleaning up bastion pod...")
        run_command(f"kubectl delete pod db-cleanup-{tenant_id}")

        progress.update(task, completed=100)

    # Verify database is dropped
    try:
        # Create temporary bastion pod
        bastion_yaml = f"""
apiVersion: v1
kind: Pod
metadata:
  name: db-verify-{tenant_id}
  annotations:
    sidecar.istio.io/inject: "false"
spec:
  securityContext:
    runAsNonRoot: true
    runAsUser: 999
    runAsGroup: 999
    fsGroup: 999
    seccompProfile:
      type: RuntimeDefault
  containers:
  - name: mysql-client
    image: mysql:8.0
    command: ["sleep", "infinity"]
    securityContext:
      allowPrivilegeEscalation: false
      runAsNonRoot: true
      runAsUser: 999
      runAsGroup: 999
      readOnlyRootFilesystem: false
      capabilities:
        drop:
        - ALL
      seccompProfile:
        type: RuntimeDefault
    resources:
      limits:
        cpu: 200m
        memory: 256Mi
      requests:
        cpu: 100m
        memory: 128Mi
    volumeMounts:
    - name: tmp-volume
      mountPath: /tmp
  volumes:
  - name: tmp-volume
    emptyDir: {{}}
  restartPolicy: Never
"""
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write(bastion_yaml)
            bastion_file = f.name

        run_command(f"kubectl apply -f {bastion_file}")
        os.unlink(bastion_file)

        # Wait for bastion pod to be ready
        run_command(f"kubectl wait --for=condition=ready pod/db-verify-{tenant_id} --timeout=60s")

        # Check if database exists
        result = run_command(f"""
        kubectl exec db-verify-{tenant_id} -- bash -c "MYSQL_PWD='{rds_password}' mysql -h {rds_host} -P {rds_port} -u {rds_user} -e \\"SHOW DATABASES LIKE '{db_name}'\\""
        """, check=False)

        # Clean up bastion pod
        run_command(f"kubectl delete pod db-verify-{tenant_id}")

        if db_name in result.stdout:
            print_error(f"Database {db_name} still exists")
            return False
        else:
            print_success(f"Database {db_name} cleaned up successfully")
            return True
    except Exception as e:
        print_warning(f"Failed to verify database cleanup: {e}")
        return True

def cleanup_s3_resources(tenant_id: str) -> bool:
    """Clean up ALL S3 resources for the tenant including PVCs, PVs, and storage classes."""
    print_step("6", f"Cleaning up ALL S3 resources for tenant-{tenant_id}")

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TimeElapsedColumn(),
        console=console
    ) as progress:
        task = progress.add_task(f"Cleaning up S3 resources for tenant-{tenant_id}...", total=100)

        # Step 1: Clean up S3 PVCs
        progress.update(task, advance=15, description="Cleaning up S3 PVCs...")
        try:
            # Delete PVCs in tenant namespace
            run_command(f"kubectl delete pvc -n tenant-{tenant_id} --all --ignore-not-found", check=False)

            # Delete specific S3 PVCs that might exist outside namespace
            pvc_patterns = [
                f"s3-pvc-{tenant_id}",
                f"s3-pvc-{tenant_id}-secure",
                f"s3-pvc-{tenant_id}-*"
            ]

            for pattern in pvc_patterns:
                run_command(f"kubectl get pvc --all-namespaces | grep {pattern} | awk '{{print $1\" \"$2}}' | xargs -r -n2 kubectl delete pvc -n", check=False)
        except Exception as e:
            print_warning(f"Failed to clean up some S3 PVCs: {e}")

        # Step 2: Clean up S3 PVs
        progress.update(task, advance=15, description="Cleaning up S3 PVs...")
        try:
            # Find and delete all PVs related to this tenant
            pv_patterns = [
                f"s3-pv-{tenant_id}",
                f"s3-pv-{tenant_id}-secure",
                f"s3-pv-{tenant_id}-*"
            ]

            for pattern in pv_patterns:
                # Get PVs matching pattern
                result = run_command(f"kubectl get pv | grep {pattern} | awk '{{print $1}}'", check=False)
                if result.stdout.strip():
                    pv_names = result.stdout.strip().split('\n')
                    for pv_name in pv_names:
                        if pv_name.strip():
                            run_command(f"kubectl delete pv {pv_name.strip()} --ignore-not-found", check=False)

            # Also clean up any PVs with tenant ID in the claim reference
            result = run_command(f"kubectl get pv -o json | jq -r '.items[] | select(.spec.claimRef.namespace == \"tenant-{tenant_id}\") | .metadata.name'", check=False)
            if result.stdout.strip():
                pv_names = result.stdout.strip().split('\n')
                for pv_name in pv_names:
                    if pv_name.strip():
                        run_command(f"kubectl delete pv {pv_name.strip()} --ignore-not-found", check=False)

        except Exception as e:
            print_warning(f"Failed to clean up some S3 PVs: {e}")

        # Step 3: Clean up S3 Storage Classes
        progress.update(task, advance=15, description="Cleaning up S3 Storage Classes...")
        try:
            # Find and delete storage classes related to this tenant
            sc_patterns = [
                f"s3-sc-{tenant_id}",
                f"s3-sc-{tenant_id}-secure",
                f"s3-sc-{tenant_id}-*"
            ]

            for pattern in sc_patterns:
                result = run_command(f"kubectl get storageclass | grep {pattern} | awk '{{print $1}}'", check=False)
                if result.stdout.strip():
                    sc_names = result.stdout.strip().split('\n')
                    for sc_name in sc_names:
                        if sc_name.strip():
                            run_command(f"kubectl delete storageclass {sc_name.strip()} --ignore-not-found", check=False)

        except Exception as e:
            print_warning(f"Failed to clean up some S3 Storage Classes: {e}")

        # Step 4: Clean up S3 Bucket
        progress.update(task, advance=25, description="Cleaning up S3 bucket...")
        bucket_name = f"tenant-{tenant_id}-assets"
        try:
            # Check if bucket exists
            s3_client.head_bucket(Bucket=bucket_name)

            # Empty bucket
            try:
                # Delete objects
                paginator = s3_client.get_paginator('list_objects_v2')
                for page in paginator.paginate(Bucket=bucket_name):
                    if 'Contents' in page:
                        objects = [{'Key': obj['Key']} for obj in page['Contents']]
                        s3_client.delete_objects(
                            Bucket=bucket_name,
                            Delete={'Objects': objects}
                        )

                # Delete versioned objects if versioning is enabled
                try:
                    versioning = s3_client.get_bucket_versioning(Bucket=bucket_name)
                    if 'Status' in versioning and versioning['Status'] == 'Enabled':
                        paginator = s3_client.get_paginator('list_object_versions')
                        for page in paginator.paginate(Bucket=bucket_name):
                            delete_keys = []
                            if 'Versions' in page:
                                delete_keys.extend([{'Key': obj['Key'], 'VersionId': obj['VersionId']} for obj in page['Versions']])
                            if 'DeleteMarkers' in page:
                                delete_keys.extend([{'Key': obj['Key'], 'VersionId': obj['VersionId']} for obj in page['DeleteMarkers']])

                            if delete_keys:
                                s3_client.delete_objects(
                                    Bucket=bucket_name,
                                    Delete={'Objects': delete_keys}
                                )
                except Exception as e:
                    print_warning(f"Failed to delete versioned objects: {e}")
            except Exception as e:
                print_warning(f"Failed to empty bucket {bucket_name}: {e}")

            # Delete bucket
            try:
                s3_client.delete_bucket(Bucket=bucket_name)
                print_success(f"S3 bucket {bucket_name} deleted successfully")
            except Exception as e:
                print_warning(f"Failed to delete bucket {bucket_name}: {e}")

        except Exception as e:
            print_info(f"Bucket {bucket_name} does not exist or already deleted: {e}")

        # Step 5: Clean up CSI driver resources
        progress.update(task, advance=15, description="Cleaning up CSI driver resources...")
        try:
            # Clean up any CSI driver specific resources
            run_command(f"kubectl delete csidriver s3-csi-driver-{tenant_id} --ignore-not-found", check=False)
            run_command(f"kubectl delete csistoragecapacity -l tenant={tenant_id} --ignore-not-found", check=False)
        except Exception as e:
            print_warning(f"Failed to clean up some CSI resources: {e}")

        # Step 6: Clean up any remaining volume attachments
        progress.update(task, advance=15, description="Cleaning up volume attachments...")
        try:
            result = run_command(f"kubectl get volumeattachment | grep {tenant_id} | awk '{{print $1}}'", check=False)
            if result.stdout.strip():
                va_names = result.stdout.strip().split('\n')
                for va_name in va_names:
                    if va_name.strip():
                        run_command(f"kubectl delete volumeattachment {va_name.strip()} --ignore-not-found", check=False)
        except Exception as e:
            print_warning(f"Failed to clean up some volume attachments: {e}")

        progress.update(task, completed=100)

    print_success(f"ALL S3 resources for tenant-{tenant_id} cleaned up successfully")
    return True

def cleanup_falco_resources(tenant_id: str) -> bool:
    """Clean up Falco security rules for the tenant."""
    print_step("6.1", f"Cleaning up Falco security rules for tenant-{tenant_id}")

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TimeElapsedColumn(),
        console=console
    ) as progress:
        task = progress.add_task(f"Cleaning up Falco resources for tenant-{tenant_id}...", total=100)

        # Clean up tenant-specific Falco rules ConfigMap
        progress.update(task, advance=50, description="Cleaning up Falco rules ConfigMap...")
        try:
            falco_configmap_name = f"falco-tenant-{tenant_id}-rules"
            run_command(f"kubectl delete configmap {falco_configmap_name} -n falco-system --ignore-not-found")
            print_success(f"Falco ConfigMap {falco_configmap_name} deleted successfully")
        except Exception as e:
            print_warning(f"Failed to clean up Falco ConfigMap: {e}")

        # Clean up any other Falco-related resources for this tenant
        progress.update(task, advance=50, description="Cleaning up additional Falco resources...")
        try:
            # Clean up any tenant-specific Falco rules or policies
            run_command(f"kubectl delete falcorule -l tenant={tenant_id} --all-namespaces --ignore-not-found", check=False)
            run_command(f"kubectl delete falcopolicy -l tenant={tenant_id} --all-namespaces --ignore-not-found", check=False)
        except Exception as e:
            print_warning(f"Failed to clean up additional Falco resources: {e}")

        progress.update(task, completed=100)

    print_success(f"Falco security rules for tenant-{tenant_id} cleaned up successfully")
    return True

def cleanup_alb_resources(tenant_id: str) -> bool:
    """Clean up AWS ALB resources for the tenant."""
    print_step("6.1.5", f"Cleaning up ALB resources for tenant-{tenant_id}")

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TimeElapsedColumn(),
        console=console
    ) as progress:
        task = progress.add_task(f"Cleaning up ALB resources for tenant-{tenant_id}...", total=100)

        # Clean up ALB ingresses first
        progress.update(task, advance=50, description="Cleaning up ALB ingresses...")
        try:
            # Remove finalizers from ALB ingresses to prevent hanging
            result = run_command(f"kubectl get ingress -n tenant-{tenant_id} -o name", check=False)
            if result.returncode == 0 and result.stdout.strip():
                ingress_names = result.stdout.strip().split('\n')
                for ingress_name in ingress_names:
                    if ingress_name.strip():
                        # Remove finalizers first
                        run_command(f"kubectl patch {ingress_name} -n tenant-{tenant_id} -p '{{\"metadata\":{{\"finalizers\":null}}}}' --type=merge", check=False)
                        # Then delete the ingress
                        run_command(f"kubectl delete {ingress_name} -n tenant-{tenant_id} --ignore-not-found", check=False)
                        print_success(f"Cleaned up {ingress_name}")
        except Exception as e:
            print_warning(f"Failed to clean up ALB ingresses: {e}")

        # Clean up any remaining ALB-related resources
        progress.update(task, advance=50, description="Cleaning up remaining ALB resources...")
        try:
            # Clean up target groups and load balancers via AWS CLI if available
            run_command(f"aws elbv2 describe-load-balancers --query 'LoadBalancers[?contains(LoadBalancerName, `{tenant_id}`)]' --output table", check=False)
        except Exception as e:
            print_warning(f"Failed to query ALB resources: {e}")

        progress.update(task, completed=100)

    print_success(f"ALB resources for tenant-{tenant_id} cleaned up successfully")
    return True

def cleanup_cluster_wide_resources(tenant_id: str) -> bool:
    """Clean up cluster-wide resources for the tenant."""
    print_step("6.2", f"Cleaning up cluster-wide resources for tenant-{tenant_id}")

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TimeElapsedColumn(),
        console=console
    ) as progress:
        task = progress.add_task(f"Cleaning up cluster-wide resources for tenant-{tenant_id}...", total=100)

        # Clean up ClusterRoles
        progress.update(task, advance=20, description="Cleaning up ClusterRoles...")
        try:
            cluster_role_patterns = [
                f"tenant-{tenant_id}-role",
                f"tenant-{tenant_id}-*-role",
                f"{tenant_id}-cluster-role"
            ]

            for pattern in cluster_role_patterns:
                result = run_command(f"kubectl get clusterrole | grep {pattern} | awk '{{print $1}}'", check=False)
                if result.stdout.strip():
                    role_names = result.stdout.strip().split('\n')
                    for role_name in role_names:
                        if role_name.strip():
                            run_command(f"kubectl delete clusterrole {role_name.strip()} --ignore-not-found", check=False)
        except Exception as e:
            print_warning(f"Failed to clean up some ClusterRoles: {e}")

        # Clean up ClusterRoleBindings
        progress.update(task, advance=20, description="Cleaning up ClusterRoleBindings...")
        try:
            cluster_role_binding_patterns = [
                f"tenant-{tenant_id}-binding",
                f"tenant-{tenant_id}-*-binding",
                f"{tenant_id}-cluster-binding"
            ]

            for pattern in cluster_role_binding_patterns:
                result = run_command(f"kubectl get clusterrolebinding | grep {pattern} | awk '{{print $1}}'", check=False)
                if result.stdout.strip():
                    binding_names = result.stdout.strip().split('\n')
                    for binding_name in binding_names:
                        if binding_name.strip():
                            run_command(f"kubectl delete clusterrolebinding {binding_name.strip()} --ignore-not-found", check=False)
        except Exception as e:
            print_warning(f"Failed to clean up some ClusterRoleBindings: {e}")

        # Clean up ValidatingAdmissionWebhooks
        progress.update(task, advance=20, description="Cleaning up ValidatingAdmissionWebhooks...")
        try:
            result = run_command(f"kubectl get validatingadmissionwebhook | grep {tenant_id} | awk '{{print $1}}'", check=False)
            if result.stdout.strip():
                webhook_names = result.stdout.strip().split('\n')
                for webhook_name in webhook_names:
                    if webhook_name.strip():
                        run_command(f"kubectl delete validatingadmissionwebhook {webhook_name.strip()} --ignore-not-found", check=False)
        except Exception as e:
            print_warning(f"Failed to clean up some ValidatingAdmissionWebhooks: {e}")

        # Clean up MutatingAdmissionWebhooks
        progress.update(task, advance=20, description="Cleaning up MutatingAdmissionWebhooks...")
        try:
            result = run_command(f"kubectl get mutatingadmissionwebhook | grep {tenant_id} | awk '{{print $1}}'", check=False)
            if result.stdout.strip():
                webhook_names = result.stdout.strip().split('\n')
                for webhook_name in webhook_names:
                    if webhook_name.strip():
                        run_command(f"kubectl delete mutatingadmissionwebhook {webhook_name.strip()} --ignore-not-found", check=False)
        except Exception as e:
            print_warning(f"Failed to clean up some MutatingAdmissionWebhooks: {e}")

        # Clean up CustomResourceDefinitions
        progress.update(task, advance=20, description="Cleaning up CustomResourceDefinitions...")
        try:
            result = run_command(f"kubectl get crd | grep {tenant_id} | awk '{{print $1}}'", check=False)
            if result.stdout.strip():
                crd_names = result.stdout.strip().split('\n')
                for crd_name in crd_names:
                    if crd_name.strip():
                        run_command(f"kubectl delete crd {crd_name.strip()} --ignore-not-found", check=False)
        except Exception as e:
            print_warning(f"Failed to clean up some CustomResourceDefinitions: {e}")

        progress.update(task, completed=100)

    print_success(f"Cluster-wide resources for tenant-{tenant_id} cleaned up successfully")
    return True

def force_namespace_cleanup(tenant_id: str) -> bool:
    """Force cleanup of namespace by removing finalizers if stuck."""
    namespace = f"tenant-{tenant_id}"
    print_step("6.3", f"Force cleaning up namespace {namespace}")

    try:
        # Check if namespace exists and is in Terminating state
        result = run_command(f"kubectl get namespace {namespace} -o jsonpath='{{.status.phase}}'", check=False)
        if result.returncode != 0:
            print_success(f"Namespace {namespace} already deleted")
            return True

        phase = result.stdout.strip()
        if phase == "Terminating":
            print_warning(f"Namespace {namespace} is stuck in Terminating state. Forcing cleanup...")

            # First, remove finalizers from ALB ingresses (common cause of stuck namespaces)
            try:
                print_info("Removing finalizers from ALB ingresses...")
                result = run_command(f"kubectl get ingress -n {namespace} -o name", check=False)
                if result.returncode == 0 and result.stdout.strip():
                    ingress_names = result.stdout.strip().split('\n')
                    for ingress_name in ingress_names:
                        if ingress_name.strip():
                            run_command(f"kubectl patch {ingress_name} -n {namespace} -p '{{\"metadata\":{{\"finalizers\":null}}}}' --type=merge", check=False)
                            print_info(f"Removed finalizers from {ingress_name}")
            except Exception as e:
                print_warning(f"Failed to remove ingress finalizers: {e}")

            # Remove finalizers from other common stuck resources
            try:
                print_info("Removing finalizers from other stuck resources...")
                # Remove finalizers from PVCs
                run_command(f"kubectl patch pvc -n {namespace} --all -p '{{\"metadata\":{{\"finalizers\":null}}}}' --type=merge", check=False)
                # Remove finalizers from services
                run_command(f"kubectl patch service -n {namespace} --all -p '{{\"metadata\":{{\"finalizers\":null}}}}' --type=merge", check=False)
            except Exception as e:
                print_warning(f"Failed to remove other finalizers: {e}")

            # Wait for resources to be cleaned up
            time.sleep(10)

            # Remove finalizers from the namespace itself
            run_command(f"kubectl patch namespace {namespace} -p '{{\"metadata\":{{\"finalizers\":null}}}}' --type=merge", check=False)

            # Wait a bit for the namespace to be deleted
            time.sleep(5)

            # Check if it's gone
            result = run_command(f"kubectl get namespace {namespace}", check=False)
            if result.returncode != 0:
                print_success(f"Namespace {namespace} successfully force-deleted")
                return True
            else:
                print_warning(f"Namespace {namespace} still exists after force cleanup")
                return False
        else:
            print_success(f"Namespace {namespace} is in {phase} state")
            return True

    except Exception as e:
        print_error(f"Failed to force cleanup namespace {namespace}: {e}")
        return False

def cleanup_istio_resources(tenant_id: str) -> bool:
    """Clean up Istio resources for the tenant."""
    print_step("6.4", f"Cleaning up Istio resources for tenant-{tenant_id}")

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TimeElapsedColumn(),
        console=console
    ) as progress:
        task = progress.add_task(f"Cleaning up Istio resources for tenant-{tenant_id}...", total=100)

        # Clean up VirtualServices (all namespaces)
        progress.update(task, advance=25, description="Cleaning up VirtualServices...")
        try:
            run_command(f"kubectl delete virtualservice -l tenant={tenant_id} --all-namespaces --ignore-not-found", check=False)
            # Try to delete specific VirtualService in common namespaces
            for ns in [f"tenant-{tenant_id}", "istio-system", "default"]:
                run_command(f"kubectl delete virtualservice tenant-{tenant_id}-vs -n {ns} --ignore-not-found", check=False)
        except Exception as e:
            print_warning(f"Failed to clean up some VirtualServices: {e}")

        # Clean up DestinationRules (all namespaces)
        progress.update(task, advance=25, description="Cleaning up DestinationRules...")
        try:
            run_command(f"kubectl delete destinationrule -l tenant={tenant_id} --all-namespaces --ignore-not-found", check=False)
            # Try to delete specific DestinationRule in common namespaces
            for ns in [f"tenant-{tenant_id}", "istio-system", "default"]:
                run_command(f"kubectl delete destinationrule tenant-{tenant_id}-dr -n {ns} --ignore-not-found", check=False)
        except Exception as e:
            print_warning(f"Failed to clean up some DestinationRules: {e}")

        # Clean up PeerAuthentications (all namespaces)
        progress.update(task, advance=25, description="Cleaning up PeerAuthentications...")
        try:
            run_command(f"kubectl delete peerauthentication -l tenant={tenant_id} --all-namespaces --ignore-not-found", check=False)
            # Try to delete specific PeerAuthentication in common namespaces
            for ns in [f"tenant-{tenant_id}", "istio-system", "default"]:
                run_command(f"kubectl delete peerauthentication tenant-{tenant_id}-pa -n {ns} --ignore-not-found", check=False)
        except Exception as e:
            print_warning(f"Failed to clean up some PeerAuthentications: {e}")

        # Clean up Gateways (all namespaces)
        progress.update(task, advance=25, description="Cleaning up Gateways...")
        try:
            run_command(f"kubectl delete gateway -l tenant={tenant_id} --all-namespaces --ignore-not-found", check=False)
            # Try to delete specific Gateway in common namespaces
            for ns in [f"tenant-{tenant_id}", "istio-system", "default"]:
                run_command(f"kubectl delete gateway tenant-{tenant_id}-gateway -n {ns} --ignore-not-found", check=False)
        except Exception as e:
            print_warning(f"Failed to clean up some Gateways: {e}")

        progress.update(task, completed=100)

    print_success(f"Istio resources for tenant-{tenant_id} cleaned up successfully")
    return True

def verify_offboarding(tenant_id: str) -> bool:
    """Verify that the tenant has been offboarded successfully."""
    print_step("7", f"Verifying offboarding for tenant-{tenant_id}")

    verification_results = []

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TimeElapsedColumn(),
        console=console
    ) as progress:
        task = progress.add_task(f"Verifying offboarding for tenant-{tenant_id}...", total=100)

        # Verify namespace is deleted
        progress.update(task, advance=30, description="Verifying namespace is deleted...")
        result = run_command(f"kubectl get namespace tenant-{tenant_id} 2>/dev/null || echo 'not found'", check=False)
        if "not found" in result.stdout:
            verification_results.append(("Namespace", "✓"))
        else:
            verification_results.append(("Namespace", "✗"))

        # Verify database is deleted
        progress.update(task, advance=30, description="Verifying database is deleted...")
        db_name = f"tenant_{tenant_id.replace('-', '_')}"

        # Get RDS credentials from AWS Secrets Manager
        try:
            if get_rds_credentials:
                rds_credentials = get_rds_credentials("production/rds/credentials")
                rds_host = rds_credentials['host']
                rds_port = rds_credentials['port']
                rds_user = rds_credentials['username']
                rds_password = rds_credentials['password']
            else:
                print_error("Security modules not available. Cannot retrieve RDS credentials.")
                return False
        except Exception as e:
            print_error(f"Failed to retrieve RDS credentials: {e}")
            return False

        # Create temporary bastion pod
        bastion_yaml = f"""
apiVersion: v1
kind: Pod
metadata:
  name: db-verify-final-{tenant_id}
  annotations:
    sidecar.istio.io/inject: "false"
spec:
  securityContext:
    runAsNonRoot: true
    runAsUser: 999
    runAsGroup: 999
    fsGroup: 999
    seccompProfile:
      type: RuntimeDefault
  containers:
  - name: mysql-client
    image: mysql:8.0
    command: ["sleep", "infinity"]
    securityContext:
      allowPrivilegeEscalation: false
      runAsNonRoot: true
      runAsUser: 999
      runAsGroup: 999
      readOnlyRootFilesystem: false
      capabilities:
        drop:
        - ALL
      seccompProfile:
        type: RuntimeDefault
    resources:
      limits:
        cpu: 200m
        memory: 256Mi
      requests:
        cpu: 100m
        memory: 128Mi
    volumeMounts:
    - name: tmp-volume
      mountPath: /tmp
  volumes:
  - name: tmp-volume
    emptyDir: {{}}
  restartPolicy: Never
"""
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write(bastion_yaml)
            bastion_file = f.name

        try:
            run_command(f"kubectl apply -f {bastion_file}")
            os.unlink(bastion_file)

            # Wait for bastion pod to be ready
            run_command(f"kubectl wait --for=condition=ready pod/db-verify-final-{tenant_id} --timeout=60s")

            # Check if database exists
            result = run_command(f"""
            kubectl exec db-verify-final-{tenant_id} -- bash -c "MYSQL_PWD='{rds_password}' mysql -h {rds_host} -P {rds_port} -u {rds_user} -e \\"SHOW DATABASES LIKE '{db_name}'\\""
            """, check=False)

            # Clean up bastion pod
            run_command(f"kubectl delete pod db-verify-final-{tenant_id}")

            if db_name in result.stdout:
                verification_results.append(("Database", "✗"))
            else:
                verification_results.append(("Database", "✓"))
        except Exception as e:
            print_warning(f"Failed to verify database deletion: {e}")
            verification_results.append(("Database", "?"))

        # Verify S3 bucket is deleted
        progress.update(task, advance=15, description="Verifying S3 bucket is deleted...")
        bucket_name = f"tenant-{tenant_id}-assets"
        try:
            s3_client.head_bucket(Bucket=bucket_name)
            verification_results.append(("S3 Bucket", "✗"))
        except:
            verification_results.append(("S3 Bucket", "✓"))

        # Verify S3 PVs are deleted
        progress.update(task, advance=10, description="Verifying S3 PVs are deleted...")
        try:
            result = run_command(f"kubectl get pv | grep s3-pv-{tenant_id}", check=False)
            if result.stdout.strip():
                verification_results.append(("S3 PVs", "✗"))
            else:
                verification_results.append(("S3 PVs", "✓"))
        except Exception as e:
            verification_results.append(("S3 PVs", "?"))

        # Verify S3 Storage Classes are deleted
        progress.update(task, advance=10, description="Verifying S3 Storage Classes are deleted...")
        try:
            result = run_command(f"kubectl get storageclass | grep s3-sc-{tenant_id}", check=False)
            if result.stdout.strip():
                verification_results.append(("S3 Storage Classes", "✗"))
            else:
                verification_results.append(("S3 Storage Classes", "✓"))
        except Exception as e:
            verification_results.append(("S3 Storage Classes", "?"))

        # Verify cluster-wide resources are deleted
        progress.update(task, advance=5, description="Verifying cluster-wide resources are deleted...")
        try:
            cluster_resources_exist = False

            # Check ClusterRoles
            result = run_command(f"kubectl get clusterrole | grep tenant-{tenant_id}", check=False)
            if result.stdout.strip():
                cluster_resources_exist = True

            # Check ClusterRoleBindings
            result = run_command(f"kubectl get clusterrolebinding | grep tenant-{tenant_id}", check=False)
            if result.stdout.strip():
                cluster_resources_exist = True

            if cluster_resources_exist:
                verification_results.append(("Cluster Resources", "✗"))
            else:
                verification_results.append(("Cluster Resources", "✓"))
        except Exception as e:
            verification_results.append(("Cluster Resources", "?"))

        # Verify Falco resources are deleted
        progress.update(task, advance=5, description="Verifying Falco resources are deleted...")
        try:
            result = run_command(f"kubectl get configmap -n falco-system | grep falco-tenant-{tenant_id}-rules", check=False)
            if result.stdout.strip():
                verification_results.append(("Falco Rules", "✗"))
            else:
                verification_results.append(("Falco Rules", "✓"))
        except Exception as e:
            verification_results.append(("Falco Rules", "?"))

        # Verify Istio resources are deleted
        progress.update(task, advance=5, description="Verifying Istio resources are deleted...")
        try:
            istio_resources_exist = False

            # Check VirtualServices
            result = run_command(f"kubectl get virtualservice --all-namespaces | grep {tenant_id}", check=False)
            if result.stdout.strip():
                istio_resources_exist = True

            # Check DestinationRules
            result = run_command(f"kubectl get destinationrule --all-namespaces | grep {tenant_id}", check=False)
            if result.stdout.strip():
                istio_resources_exist = True

            if istio_resources_exist:
                verification_results.append(("Istio Resources", "✗"))
            else:
                verification_results.append(("Istio Resources", "✓"))
        except Exception as e:
            verification_results.append(("Istio Resources", "?"))

        progress.update(task, completed=100)

    # Display verification results in a table
    table = Table(title=f"Verification Results for tenant-{tenant_id}")
    table.add_column("Component", style="cyan")
    table.add_column("Status", style="green")

    all_passed = True
    for component, status in verification_results:
        if status == "✓":
            table.add_row(component, f"[green]{status}[/green]")
        elif status == "✗":
            table.add_row(component, f"[red]{status}[/red]")
            all_passed = False
        else:
            table.add_row(component, f"[yellow]{status}[/yellow]")
            all_passed = False

    console.print(table)

    if all_passed:
        print_success(f"Tenant {tenant_id} offboarded successfully")
        return True
    else:
        print_error(f"Tenant {tenant_id} offboarding verification failed")
        return False

def display_summary(tenant_id: str, start_time: float) -> None:
    """Display a summary of the tenant offboarding."""
    elapsed_time = time.time() - start_time
    minutes, seconds = divmod(elapsed_time, 60)

    print_header("Tenant Offboarding Summary")

    table = Table(title=f"Tenant {tenant_id} Offboarding Summary")
    table.add_column("Property", style="cyan")
    table.add_column("Value", style="green")

    table.add_row("Tenant ID", tenant_id)
    table.add_row("Namespace", f"tenant-{tenant_id}")
    table.add_row("Database", f"tenant_{tenant_id.replace('-', '_')}")
    table.add_row("S3 Bucket", f"tenant-{tenant_id}-assets")
    table.add_row("Elapsed Time", f"{int(minutes)}m {int(seconds)}s")

    console.print(table)

async def offboard_tenant(
    tenant_id: str,
    backup: bool = False,
    backup_path: str = DEFAULT_BACKUP_PATH,
    skip_db_cleanup: bool = False,
    skip_s3_cleanup: bool = False,
    skip_namespace_cleanup: bool = False,
    rds_secret_name: str = DEFAULT_RDS_SECRET_NAME,
    verify: bool = True,
    force: bool = False,
    debug: bool = False
) -> bool:
    """Offboard a tenant with all components."""
    if debug:
        logger.setLevel(logging.DEBUG)

    start_time = time.time()

    print_header(f"Starting Tenant Offboarding for {tenant_id}")

    # Create backup if requested
    if backup:
        if not create_backup(tenant_id, backup_path):
            print_error(f"Failed to create backup for tenant-{tenant_id}")
            if not force:
                return False

    # Clean up monitoring resources
    if not cleanup_monitoring(tenant_id):
        print_error(f"Failed to clean up monitoring resources for tenant-{tenant_id}")
        if not force:
            return False

    # Clean up autoscaling resources
    if not cleanup_autoscaling(tenant_id):
        print_error(f"Failed to clean up autoscaling resources for tenant-{tenant_id}")
        if not force:
            return False

    # Clean up namespace
    if not skip_namespace_cleanup:
        if not cleanup_namespace(tenant_id, force):
            print_error(f"Failed to clean up namespace for tenant-{tenant_id}")
            if not force:
                return False

    # Clean up database
    if not skip_db_cleanup:
        if not cleanup_database(tenant_id, rds_secret_name):
            print_error(f"Failed to clean up database for tenant-{tenant_id}")
            if not force:
                return False

    # Clean up ALL S3 resources (PVCs, PVs, Storage Classes, Buckets)
    if not skip_s3_cleanup:
        if not cleanup_s3_resources(tenant_id):
            print_error(f"Failed to clean up S3 resources for tenant-{tenant_id}")
            if not force:
                return False

    # Clean up Falco security rules
    if not cleanup_falco_resources(tenant_id):
        print_error(f"Failed to clean up Falco resources for tenant-{tenant_id}")
        if not force:
            return False

    # Clean up ALB resources
    if not cleanup_alb_resources(tenant_id):
        print_error(f"Failed to clean up ALB resources for tenant-{tenant_id}")
        if not force:
            return False

    # Clean up cluster-wide resources
    if not cleanup_cluster_wide_resources(tenant_id):
        print_error(f"Failed to clean up cluster-wide resources for tenant-{tenant_id}")
        if not force:
            return False

    # Force cleanup namespace if stuck
    if not force_namespace_cleanup(tenant_id):
        print_error(f"Failed to force cleanup namespace for tenant-{tenant_id}")
        if not force:
            return False

    # Clean up Istio resources
    if not cleanup_istio_resources(tenant_id):
        print_error(f"Failed to clean up Istio resources for tenant-{tenant_id}")
        if not force:
            return False

    # Verify offboarding
    if verify:
        if not verify_offboarding(tenant_id):
            print_warning(f"Verification failed for tenant-{tenant_id}")
            # Continue anyway, as this is just a verification step

    # Display summary
    display_summary(tenant_id, start_time)

    print_success(f"Tenant {tenant_id} offboarded successfully")
    return True

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Advanced Tenant Offboarding Script")
    parser.add_argument("--tenant-id", required=True, help="Tenant ID (e.g., acme-corp)")
    parser.add_argument("--backup", action="store_true", help="Create a backup before offboarding")
    parser.add_argument("--backup-path", default=DEFAULT_BACKUP_PATH, help=f"Backup path (default: {DEFAULT_BACKUP_PATH})")
    parser.add_argument("--skip-db-cleanup", action="store_true", help="Skip database cleanup")
    parser.add_argument("--skip-s3-cleanup", action="store_true", help="Skip S3 cleanup")
    parser.add_argument("--skip-namespace-cleanup", action="store_true", help="Skip namespace cleanup")
    parser.add_argument("--rds-secret-name", default=DEFAULT_RDS_SECRET_NAME, help="RDS secret name")
    parser.add_argument("--verify", action="store_true", help="Verify offboarding")
    parser.add_argument("--force", action="store_true", help="Force offboarding even if some steps fail")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    parser.add_argument("--no-color", action="store_true", help="Disable colored output")

    args = parser.parse_args()

    if args.no_color:
        console.no_color = True

    try:
        asyncio.run(offboard_tenant(
            tenant_id=args.tenant_id,
            backup=args.backup,
            backup_path=args.backup_path,
            skip_db_cleanup=args.skip_db_cleanup,
            skip_s3_cleanup=args.skip_s3_cleanup,
            skip_namespace_cleanup=args.skip_namespace_cleanup,
            rds_secret_name=args.rds_secret_name,
            verify=args.verify,
            force=args.force,
            debug=args.debug
        ))
    except KeyboardInterrupt:
        print_error("Tenant offboarding interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Tenant offboarding failed: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
