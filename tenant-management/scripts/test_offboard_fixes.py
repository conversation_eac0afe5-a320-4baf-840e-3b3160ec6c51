#!/usr/bin/env python3
"""
Test script to verify offboarding script fixes
"""

import subprocess
import sys
import time

def run_command(command: str, check: bool = True) -> subprocess.CompletedProcess:
    """Run a shell command and return the result."""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, check=check)
        return result
    except subprocess.CalledProcessError as e:
        if check:
            print(f"❌ Command failed: {command}")
            print(f"Error: {e.stderr}")
            raise
        return e

def test_kubectl_patch_syntax():
    """Test that kubectl patch commands use correct syntax."""
    print("🔧 Testing kubectl patch syntax fixes...")
    
    # Test that we don't use --all with patch commands
    with open("advanced_tenant_offboard.py", "r") as f:
        content = f.read()
    
    # Check for invalid patch syntax
    invalid_patterns = [
        "kubectl patch.*--all.*-p",
        "kubectl patch.*--all.*--type"
    ]
    
    issues_found = []
    for pattern in invalid_patterns:
        import re
        matches = re.findall(pattern, content)
        if matches:
            issues_found.extend(matches)
    
    if issues_found:
        print(f"❌ Found invalid kubectl patch syntax:")
        for issue in issues_found:
            print(f"   - {issue}")
        return False
    else:
        print("✅ kubectl patch syntax is correct")
        return True

def test_falco_resource_cleanup():
    """Test that Falco resource cleanup doesn't use invalid resource types."""
    print("🔧 Testing Falco resource cleanup fixes...")
    
    with open("advanced_tenant_offboard.py", "r") as f:
        content = f.read()
    
    # Check for invalid Falco resource types
    invalid_patterns = [
        "kubectl delete falcorule",
        "kubectl delete falcopolicy"
    ]
    
    issues_found = []
    for pattern in invalid_patterns:
        if pattern in content and "# Note:" not in content.split(pattern)[0].split('\n')[-1]:
            issues_found.append(pattern)
    
    if issues_found:
        print(f"❌ Found invalid Falco resource types:")
        for issue in issues_found:
            print(f"   - {issue}")
        return False
    else:
        print("✅ Falco resource cleanup is fixed")
        return True

def test_namespace_existence_checks():
    """Test that namespace operations check for existence first."""
    print("🔧 Testing namespace existence checks...")
    
    with open("advanced_tenant_offboard.py", "r") as f:
        content = f.read()
    
    # Check that namespace patching includes existence check
    if "kubectl get namespace" in content and "kubectl patch namespace" in content:
        # Look for the pattern where we check before patching
        lines = content.split('\n')
        patch_lines = [i for i, line in enumerate(lines) if "kubectl patch namespace" in line]
        
        for patch_line_num in patch_lines:
            # Look for existence check within 10 lines before the patch
            check_found = False
            for i in range(max(0, patch_line_num - 10), patch_line_num):
                if "kubectl get namespace" in lines[i]:
                    check_found = True
                    break
            
            if not check_found:
                print(f"❌ Found namespace patch without existence check at line {patch_line_num + 1}")
                return False
        
        print("✅ Namespace existence checks are in place")
        return True
    else:
        print("✅ No namespace patching found or properly structured")
        return True

def test_error_handling():
    """Test that error handling is improved."""
    print("🔧 Testing error handling improvements...")
    
    with open("advanced_tenant_offboard.py", "r") as f:
        content = f.read()
    
    # Check for proper error handling patterns
    improvements = [
        "check=False",  # Non-blocking commands
        "try:" and "except Exception as e:",  # Exception handling
        "print_warning",  # Warning messages
        "if result.returncode"  # Return code checking
    ]
    
    all_present = all(pattern in content for pattern in improvements)
    
    if all_present:
        print("✅ Error handling improvements are in place")
        return True
    else:
        print("❌ Some error handling improvements are missing")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing Offboarding Script Fixes")
    print("=" * 50)
    
    tests = [
        test_kubectl_patch_syntax,
        test_falco_resource_cleanup,
        test_namespace_existence_checks,
        test_error_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The offboarding script fixes are working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
