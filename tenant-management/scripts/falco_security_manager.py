#!/usr/bin/env python3
"""
Falco Runtime Security Manager for Tenant Management System
Deploys and configures Falco for runtime threat detection
"""

import logging
import subprocess
import tempfile
import os
import yaml
from typing import Dict, List, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FalcoSecurityManager:
    """Manages Falco runtime security for tenant deployments."""
    
    def __init__(self):
        """Initialize Falco Security Manager."""
        self.falco_namespace = "falco-system"
        self.helm_repo_name = "falcosecurity"
        self.helm_repo_url = "https://falcosecurity.github.io/charts"
        
    def install_falco_helm_repo(self) -> bool:
        """Install Falco Helm repository."""
        try:
            logger.info("📦 Adding Falco Helm repository...")
            
            # Add Helm repository
            result = subprocess.run(
                ['helm', 'repo', 'add', self.helm_repo_name, self.helm_repo_url],
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0 and "already exists" not in result.stderr:
                logger.error(f"❌ Failed to add Helm repository: {result.stderr}")
                return False
            
            # Update Helm repositories
            result = subprocess.run(
                ['helm', 'repo', 'update'],
                capture_output=True,
                text=True,
                check=True
            )
            
            logger.info("✅ Falco Helm repository added and updated")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to install Helm repository: {e.stderr}")
            return False
        except Exception as e:
            logger.error(f"❌ Failed to install Helm repository: {e}")
            return False
    
    def create_falco_namespace(self) -> bool:
        """Create Falco namespace."""
        try:
            logger.info(f"🏗️ Creating Falco namespace: {self.falco_namespace}")
            
            result = subprocess.run(
                ['kubectl', 'create', 'namespace', self.falco_namespace],
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0 and "already exists" not in result.stderr:
                logger.error(f"❌ Failed to create namespace: {result.stderr}")
                return False
            
            logger.info(f"✅ Falco namespace created: {self.falco_namespace}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create namespace: {e}")
            return False
    
    def create_falco_values(self) -> str:
        """Create Falco Helm values configuration."""
        falco_values = {
            'falco': {
                'grpc': {
                    'enabled': True,
                    'bind_address': '0.0.0.0:5060'
                },
                'grpc_output': {
                    'enabled': True
                },
                'json_output': True,
                'json_include_output_property': True,
                'log_stderr': True,
                'log_syslog': False,
                'log_level': 'info',
                'priority': 'debug',
                'buffered_outputs': False,
                'syscall_event_drops': {
                    'actions': ['log', 'alert'],
                    'rate': 0.03333,
                    'max_burst': 1000
                },
                'rules_file': [
                    '/etc/falco/falco_rules.yaml',
                    '/etc/falco/falco_rules.local.yaml',
                    '/etc/falco/k8s_audit_rules.yaml',
                    '/etc/falco/rules.d'
                ]
            },
            'driver': {
                'kind': 'ebpf'  # Use eBPF driver for better compatibility
            },
            'collectors': {
                'enabled': True,
                'docker': {
                    'enabled': False
                },
                'containerd': {
                    'enabled': True,
                    'socket': '/run/containerd/containerd.sock'
                },
                'crio': {
                    'enabled': False
                }
            },
            'services': {
                'falco': {
                    'type': 'ClusterIP',
                    'ports': {
                        'grpc': 5060
                    }
                }
            },
            'falcosidekick': {
                'enabled': True,
                'config': {
                    'slack': {
                        'webhookurl': '',
                        'channel': '#security-alerts',
                        'username': 'Falco',
                        'iconurl': 'https://falco.org/img/brand/falco-logo.png',
                        'title': 'Falco Security Alert',
                        'minimumpriority': 'warning'
                    },
                    'webhook': {
                        'address': ''
                    }
                }
            },
            'resources': {
                'requests': {
                    'cpu': '100m',
                    'memory': '512Mi'
                },
                'limits': {
                    'cpu': '1000m',
                    'memory': '1024Mi'
                }
            },
            'nodeSelector': {},
            'tolerations': [
                {
                    'effect': 'NoSchedule',
                    'key': 'node-role.kubernetes.io/master'
                },
                {
                    'effect': 'NoSchedule',
                    'key': 'node-role.kubernetes.io/control-plane'
                }
            ],
            'affinity': {},
            'podSecurityPolicy': {
                'create': False
            },
            'serviceAccount': {
                'create': True,
                'name': 'falco'
            },
            'rbac': {
                'create': True
            }
        }
        
        return yaml.dump(falco_values, default_flow_style=False)
    
    def create_tenant_specific_rules(self, tenant_id: str) -> str:
        """Create tenant-specific Falco rules."""
        tenant_rules = f"""
# Tenant-specific Falco rules for tenant-{tenant_id}
- rule: Unauthorized Process in Tenant Container
  desc: Detect unauthorized processes in tenant-{tenant_id} containers
  condition: >
    spawned_process and
    k8s.ns.name = "tenant-{tenant_id}" and
    not proc.name in (php-fpm, nginx, mysqld, redis-server, node, python, java)
  output: >
    Unauthorized process in tenant container
    (user=%user.name command=%proc.cmdline container=%container.name 
     image=%container.image.repository tenant={tenant_id})
  priority: WARNING
  tags: [tenant, process, security]

- rule: Sensitive File Access in Tenant
  desc: Detect access to sensitive files in tenant-{tenant_id}
  condition: >
    open_read and
    k8s.ns.name = "tenant-{tenant_id}" and
    (fd.name contains "/etc/passwd" or
     fd.name contains "/etc/shadow" or
     fd.name contains "/root/.ssh" or
     fd.name contains "/home/<USER>/.ssh")
  output: >
    Sensitive file accessed in tenant container
    (user=%user.name file=%fd.name container=%container.name 
     image=%container.image.repository tenant={tenant_id})
  priority: CRITICAL
  tags: [tenant, file, security]

- rule: Network Connection from Tenant Container
  desc: Monitor network connections from tenant-{tenant_id} containers
  condition: >
    outbound and
    k8s.ns.name = "tenant-{tenant_id}" and
    not fd.sip in (10.0.0.0/8, **********/12, ***********/16)
  output: >
    External network connection from tenant container
    (user=%user.name connection=%fd.name container=%container.name 
     image=%container.image.repository tenant={tenant_id})
  priority: INFO
  tags: [tenant, network, security]

- rule: Privilege Escalation in Tenant
  desc: Detect privilege escalation attempts in tenant-{tenant_id}
  condition: >
    spawned_process and
    k8s.ns.name = "tenant-{tenant_id}" and
    (proc.name in (su, sudo, setuid) or
     proc.args contains "chmod +s")
  output: >
    Privilege escalation attempt in tenant container
    (user=%user.name command=%proc.cmdline container=%container.name 
     image=%container.image.repository tenant={tenant_id})
  priority: CRITICAL
  tags: [tenant, privilege, security]
"""
        return tenant_rules
    
    def deploy_falco(self) -> bool:
        """Deploy Falco using Helm."""
        try:
            logger.info("🚀 Deploying Falco runtime security...")
            
            # Create values file
            values_content = self.create_falco_values()
            
            with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
                f.write(values_content)
                values_file = f.name
            
            try:
                # Deploy Falco using Helm
                result = subprocess.run([
                    'helm', 'upgrade', '--install', 'falco',
                    f'{self.helm_repo_name}/falco',
                    '--namespace', self.falco_namespace,
                    '--create-namespace',
                    '--values', values_file,
                    '--wait',
                    '--timeout', '10m'
                ], capture_output=True, text=True, check=True)
                
                logger.info("✅ Falco deployed successfully")
                return True
                
            finally:
                os.unlink(values_file)
                
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to deploy Falco: {e.stderr}")
            return False
        except Exception as e:
            logger.error(f"❌ Failed to deploy Falco: {e}")
            return False
    
    def create_tenant_rules_configmap(self, tenant_id: str) -> bool:
        """Create tenant-specific rules ConfigMap."""
        try:
            logger.info(f"📋 Creating tenant-specific Falco rules for tenant-{tenant_id}")
            
            rules_content = self.create_tenant_specific_rules(tenant_id)
            
            configmap_yaml = f"""
apiVersion: v1
kind: ConfigMap
metadata:
  name: falco-tenant-{tenant_id}-rules
  namespace: {self.falco_namespace}
  labels:
    app: falco
    tenant: {tenant_id}
data:
  tenant_{tenant_id}_rules.yaml: |
{rules_content}
"""
            
            with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
                f.write(configmap_yaml)
                temp_file = f.name
            
            try:
                result = subprocess.run(
                    ['kubectl', 'apply', '-f', temp_file],
                    capture_output=True,
                    text=True,
                    check=True
                )
                logger.info(f"✅ Tenant-specific Falco rules created for tenant-{tenant_id}")
                return True
                
            finally:
                os.unlink(temp_file)
                
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to create tenant rules: {e.stderr}")
            return False
        except Exception as e:
            logger.error(f"❌ Failed to create tenant rules: {e}")
            return False
    
    def verify_falco_deployment(self) -> bool:
        """Verify Falco deployment status."""
        try:
            logger.info("🔍 Verifying Falco deployment...")
            
            # Check if Falco pods are running
            result = subprocess.run([
                'kubectl', 'get', 'pods', '-n', self.falco_namespace,
                '-l', 'app.kubernetes.io/name=falco',
                '--no-headers'
            ], capture_output=True, text=True)
            
            if result.returncode == 0 and result.stdout.strip():
                pods = result.stdout.strip().split('\n')
                running_pods = [pod for pod in pods if 'Running' in pod]
                
                logger.info(f"✅ Falco verification complete:")
                logger.info(f"   - Total pods: {len(pods)}")
                logger.info(f"   - Running pods: {len(running_pods)}")
                
                return len(running_pods) > 0
            else:
                logger.warning("⚠️ No Falco pods found")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to verify Falco deployment: {e}")
            return False

def main():
    """Main function for Falco Security Manager."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Falco Runtime Security Manager')
    parser.add_argument('--action', choices=['install', 'verify', 'tenant-rules'], 
                       default='install', help='Action to perform')
    parser.add_argument('--tenant-id', help='Tenant ID for tenant-specific rules')
    
    args = parser.parse_args()
    
    # Initialize Falco Security Manager
    falco_manager = FalcoSecurityManager()
    
    if args.action == 'install':
        # Install Falco
        falco_manager.install_falco_helm_repo()
        falco_manager.create_falco_namespace()
        falco_manager.deploy_falco()
        falco_manager.verify_falco_deployment()
    elif args.action == 'verify':
        falco_manager.verify_falco_deployment()
    elif args.action == 'tenant-rules':
        if not args.tenant_id:
            logger.error("❌ Tenant ID required for tenant-rules action")
            return
        falco_manager.create_tenant_rules_configmap(args.tenant_id)

if __name__ == '__main__':
    main()
